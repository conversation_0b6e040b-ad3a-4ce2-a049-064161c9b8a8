name: AI - Site Issue

on:
  issues:
    types: [opened, labeled]

permissions:
  contents: write
  pull-requests: write
  actions: read
  issues: write
  id-token: write

jobs:
  attempt-fix:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    if: contains(github.event.issue.labels.*.name, 'ai-site')

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create instruction file
        run: |
          cat > /tmp/review-instruction.txt << EOF
          You are operating in a GitHub Actions runner to automatically analyze and fix GitHub issues.

          The GitHub CLI is available as \'gh\' and authenticated via \'GH_TOKEN\'. Git is available.

          # Context:
          - Repository: ${{ github.repository }}
          - Default Branch: ${{ github.event.repository.default_branch }}
          - Issue Number: ${{ github.event.issue.number }}
          - Issue Title: ${{ github.event.issue.title }}
          - Issue Author: ${{ github.event.issue.user.login }}
          - Issue Body: ${{ github.event.issue.body }}
          - Issue Labels: ${{ join(github.event.issue.labels.*.name, ', ') }}

          # Goal:
          - Analyze the issue and determine if it can be automatically fixed.
          - If a fix is possible, create a new branch from the default branch, make the changes, and push them to the repository.
          - Create a pull request from the new branch to the default branch.
          - If a fix is not possible, leave a comment on the issue indicating that a manual fix is required.

          # Guidelines:
          - Only attempt fixes for clear, well-defined issues.
          - Avoid making changes that could break existing functionality.
          - Be conservative - when in doubt, request manual review.
          - Follow the project\'s coding standards and conventions.
          EOF

      - name: Analyze Issue
        uses: augmentcode/augment-agent@v0
        with:
          augment_session_auth: ${{ secrets.AUGMENT_SESSION_AUTH }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          instruction_file: /tmp/review-instruction.txt
