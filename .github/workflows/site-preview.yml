name: Preview Site
on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - 'packages/site/**'
      - '.github/workflows/site-preview.yml'
      - 'package.json'

jobs:
  deploy-preview:
    name: Deploy preview site
    runs-on: ubuntu-latest
    
    permissions:
      pull-requests: write  # Allow commenting on PRs
      contents: read
    
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@v4
      
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          cache: "npm"
      
      - name: Install dependencies
        run: npm ci --quiet --no-progress
      
      - name: Build
        run: |
          npm run build -w site
      
      - name: Deploy Preview
        id: deploy
        run: |
          # Deploy to Cloudflare Pages with preview branch name
          OUTPUT=$(npm run publish -w site -- --branch=preview-${{ github.event.pull_request.number }} 2>&1)
          echo "$OUTPUT"
          
          # Extract the deployment URL from the output
          PREVIEW_URL=$(echo "$OUTPUT" | grep -oE 'https://[a-z0-9-]+\.individuallist-xyz\.pages\.dev' | head -n1)
          
          if [ -z "$PREVIEW_URL" ]; then
            # Fallback URL pattern if extraction fails
            PREVIEW_URL="https://preview-${{ github.event.pull_request.number }}.individuallist-xyz.pages.dev"
          fi
          
          echo "preview_url=$PREVIEW_URL" >> $GITHUB_OUTPUT
        env:
          CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          AUTH_SECRET: ${{ secrets.AUTH_SECRET }}
          BACKBLAZE_KEY: ${{ secrets.BACKBLAZE_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      
      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          script: |
            const preview_url = '${{ steps.deploy.outputs.preview_url }}';
            const comment = `### 🚀 Preview Deployment
            
            Your preview site has been deployed successfully!
            
            🔗 **Preview URL:** ${preview_url}
            
            This preview will be updated automatically with new commits to this PR.`;
            
            // Find existing comment
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
            });
            
            const botComment = comments.find(comment => 
              comment.user?.type === 'Bot' && 
              comment.body?.includes('Preview Deployment')
            );
            
            if (botComment) {
              // Update existing comment
              await github.rest.issues.updateComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id,
                body: comment
              });
            } else {
              // Create new comment
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: context.issue.number,
                body: comment
              });
            }
