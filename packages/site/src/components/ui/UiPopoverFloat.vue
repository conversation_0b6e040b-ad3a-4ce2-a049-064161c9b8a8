<script setup lang="ts">
const props = defineProps<{
  placement: 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  cssClass?: string
  opened?: () => void | Promise<void>
}>()

defineExpose({
  show: () => setOpen(true),
})

const open = ref(false)

async function setOpen(show: boolean) {
  open.value = show
  if (show && props.opened) {
    await Promise.resolve(props.opened())
  }
}
</script>

<template>
  <span class="popover-float-trigger">
    <slot name="trigger">
      <button class="compact" @click="() => setOpen(!open)">
        <slot name="trigger-title">
          Popover
        </slot>
      </button>
    </slot>
  </span>
  <Teleport v-if="open" to="body" v-bind="$attrs">
    <div class="popover-float-wrap theme theme-panel" :class="[placement ?? 'top-left', cssClass ?? '']">
      <header class="popover-header">
        <div class="left" />
        <div class="right">
          <button class="compact" @click="() => setOpen(false)">
            <span class="icon-close" />
          </button>
        </div>
      </header>
      <div class="popover-body">
        <slot />
      </div>
    </div>
  </Teleport>
</template>

<style>
:root {
  --pop-float-top: var(--padding-small);
  --pop-float-right: var(--padding-small);
  --pop-float-bottom: var(--padding-small);
  --pop-float-left: var(--padding-small);
  --pop-h: min(var(--base-size) * 15, 100svh - var(--pop-float-bottom) - var(--pop-float-top));
}

.popover-float-wrap {
  position: fixed;
  z-index: var(--z-index-float);
  height: var(--pop-h);
  width: min(var(--base-size) * 10, 100vw - var(--padding-base));
  overflow: clip;
  border-radius: var(--corner-radius);
  display: grid;
  grid-template-rows: 1fr;
  box-shadow: 0 0 30px 15px rgba(0 0 0 / 0.25);

  &[class*='top'] {
    top: var(--pop-float-top);
  }

  &[class*='bottom'] {
    bottom: var(--pop-float-bottom);
  }

  &[class*='left'] {
    left: var(--pop-float-left);
  }

  &[class*='right'] {
    right: var(--pop-float-right);
  }

  .popover-header {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 100;
  }

  .popover-body {
    height: 100%;
    overflow: auto;
    overscroll-behavior: contain;
  }
}
</style>
