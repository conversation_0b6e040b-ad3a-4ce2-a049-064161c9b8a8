<script setup lang="ts">
// export type ResponsiveMenuWidth = 'xs' | 's' | 'm' | 'l' | 'xl'
// defineProps<{
//   minWidth?: ResponsiveMenuWidth
// }>()
</script>

<template>
  <UiPopup class="ui-responsive-menu">
    <template #trigger-title>
      <slot name="trigger-title">
        <span class="icon-more horizontal" />
      </slot>
    </template>
    <div class="ui-responsive-menu-content">
      <slot />
    </div>
  </UiPopup>
  <div class="ui-responsive-menu-wide">
    <slot />
  </div>
</template>

<style>
.ui-responsive-menu {
  position: relative;
  button {
    --icon-size: 1.3rem;
    min-height: 0;
    height: auto;
    width: calc(var(--base-size) * 2);
    justify-content: flex-start;
  }
  @container (width > 768px) {
    display: none;
  }
}
.ui-responsive-menu-content {
  padding: var(--padding-small);
}
.ui-responsive-menu-wide {
  @container (width <= 768px) {
    display: none;
  }
}
.icon-more.horizontal {
  rotate: 90deg;
}
</style>
