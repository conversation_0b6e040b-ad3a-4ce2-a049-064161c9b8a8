<script setup lang="ts">
import { listenForClickOutsideElements, stopListeningForClickOutside } from '@/lib/click-outside-listener'

interface Props {
  open: boolean
  left?: string
  top?: string
  right?: string
  bottom?: string
  triggerSelector?: string
}
const props = withDefaults(defineProps<Props>(), {
  left: '',
  top: '',
  right: '0px',
  bottom: '',
  triggerSelector: '',
})
const emit = defineEmits(['request-close'])
const { open, left, top, right, bottom, triggerSelector } = toRefs(props)
const panel = ref(null)

const direction = computed(() => {
  if (parseFloat(top.value) === 0) {
    return 'top'
  }
  if (parseFloat(bottom.value) === 0) {
    return 'bottom'
  }
  if (parseFloat(left.value) === 0) {
    return 'left'
  }
  if (parseFloat(right.value) === 0) {
    return 'right'
  }
})

let clickListener: (event: Event) => void
onMounted(async () => {
  await nextTick()
  const triggerEl = document.querySelector(triggerSelector.value)
  clickListener = listenForClickOutsideElements([panel.value, triggerEl], () => { emit('request-close') })
})
onUnmounted(() => stopListeningForClickOutside(clickListener))
</script>

<template>
  <ClientOnly>
    <Teleport to="#overlays">
      <div ref="panel" class="sliding-panel" :class="[{ open }, direction]" :style="{ left, top, right, bottom }">
        <slot />
      </div>
    </Teleport>
  </ClientOnly>
</template>

<style lang='scss'>
.sliding-panel {
  position: absolute;
  transition: transform 0.2s;
  overflow: hidden;
  box-shadow: 0 8px 8px rgb(0 0 0 / 0.03);
  border: 1px solid white;
  border-radius: var(--padding-small);

  &.right {
    transform: translateX(100%);
  }

  &.left {
    transform: translateX(-100%);
  }

  &.top {
    transform: translateY(-100%);
  }

  &.bottom {
    transform: translateY(100%);
  }

  &.open {
    --open-margin: var(--padding-small);

    &.right {
      transform: translateX(calc(var(--open-margin) * -1));
    }

    &.left {
      transform: translateX(var(--open-margin));
    }

    &.top {
      transform: translateY(var(--open-margin));
    }

    &.bottom {
      transform: translateY(calc(var(--open-margin) * -1));
    }
  }
}
</style>
