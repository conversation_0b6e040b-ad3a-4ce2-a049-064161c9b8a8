<script setup>
import { displayableOklch, interpolateOklch, maxContrastColor, getSupportedColorGamut } from '~/lib/color/culori'

const props = defineProps({
  oklch: { type: Array, default: () => [100, 0, 0] },
})

const colorGamut = ref('rgb')
onMounted(() => {
  colorGamut.value = getSupportedColorGamut()
})

const emit = defineEmits(['update:oklch'])
const maxL = 1
const maxC = 0.37
const maxH = 360

const l = computed(() => props.oklch[0])
const c = computed(() => props.oklch[1])
const h = computed(() => props.oklch[2])

const li = computed({
  get: () => {
    return reverseEaseOutCirc(l.value)
  },
  set: (val) => {
    const { l, c, h } = interpolateL.value(val)
    emit('update:oklch', [l, c, h])
  },
})

const ci = computed({
  get: () => c.value / maxC,
  set: (val) => {
    const { l, c, h } = interpolateC.value(val)
    emit('update:oklch', [l, c, h])
  },
})

const hi = computed({
  get: () => h.value / maxH,
  set: (val) => {
    const { l, c, h } = interpolateH.value(val)
    emit('update:oklch', [l, c, h])
  },
})

const displayableColor = computed(() => {
  return displayableOklch(props.oklch, colorGamut.value)
})

const interpolateL = computed(() => makeInterpolate(null, c.value, h.value, maxL, easeOutCirc))
const interpolateC = computed(() => makeInterpolate(l.value, null, h.value, maxC))
const interpolateH = computed(() => makeInterpolate(l.value, c.value, null, maxH))

const gradMaxL = 0.995
const interpolateLightC = computed(() => {
  if (l.value > gradMaxL) {
    return makeInterpolate(gradMaxL, null, h.value, maxC)
  }
  return interpolateC.value
})
const interpolateLightH = computed(() => {
  if (l.value > gradMaxL) {
    return makeInterpolate(gradMaxL, c.value, null, maxH)
  }
  return interpolateH.value
})

const trackGradientL = computed(() => makeGradient(interpolateL.value, 32))
const trackGradientC = computed(() => makeGradient(interpolateLightC.value, 16))
const trackGradientH = computed(() => makeGradient(interpolateLightH.value, 16))

const cssVars = computed(() => ({
  '--color': displayableColor.value,
  '--color-contrast': maxContrastColor(props.oklch),
  '--gradient-l': trackGradientL.value,
  '--gradient-c': trackGradientC.value,
  '--gradient-h': trackGradientH.value,
}))

function makeInterpolate(l, c, h, maxVal, easeFunc) {
  const stops = [
    { mode: 'oklch', l: l ?? 0, c: c ?? 0, h: h ?? 0 },
    { mode: 'oklch', l: l ?? maxVal, c: c ?? maxVal, h: h ?? maxVal },
  ]
  if (easeFunc) {
    stops.unshift(easeFunc)
  }
  return interpolateOklch(stops)
}

function makeGradient(interpolate, numStops) {
  const gradient = [...Array(numStops + 1)].map((_, i) => {
    const color = interpolate((1 / numStops) * i)
    return displayableOklch(color)
  })
  return `linear-gradient(to right, ${gradient.join(', ')})`
}

function easeOutCirc(t) {
  return Math.sqrt(1 - Math.pow(t - 1, 2))
}
function reverseEaseOutCirc(value) {
  return 1 - Math.sqrt(1 - Math.pow(value, 2))
}
</script>

<template>
  <div class="color-picker" :style="cssVars">
    <div class="cp__wrapper">
      <div class="palette">
        <div class="l-bar slider-wrap">
          <div class="bg">
            <input v-model="li" type="range" min="0" max="1" step="0.001" title="Lightness">
          </div>
        </div>
        <div class="c-bar slider-wrap">
          <div class="bg">
            <input v-model="ci" type="range" min="0" max="1" step="0.001" title="Chroma">
          </div>
        </div>
        <div class="h-bar slider-wrap">
          <div class="bg">
            <input v-model="hi" type="range" min="0" step="0.001" max="1" title="Hue">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.color-picker {
  width: fit-content;

  >button {
    margin: auto;
  }

  .l-bar .bg {
    background: var(--gradient-l);
  }

  .c-bar .bg {
    background: var(--gradient-c);
  }

  .h-bar .bg {
    background: var(--gradient-h);
  }

  .cp__wrapper {
    --border-w: 2px;
    --track-h: var(--size-two-third);
    --track-border-radius: calc(var(--track-h) * 0.5);
    border-radius: 0;
    user-select: none;

    .slider-wrap {
      position: relative;
      height: var(--track-h);
      display: flex;
      align-items: center;

      &:not(:last-child) {
        margin-bottom: var(--padding-base);
      }

      .bg {
        position: relative;
        width: 100%;
        height: 100%;
        border-radius: var(--track-border-radius);
      }
    }
  }
}

@mixin slider-track {
  position: relative;
  width: 100%;
  height: var(--track-h);
  background: transparent;
  border-radius: var(--track-border-radius);
  box-sizing: border-box;
}

@mixin slider-thumb {
  --thumb-size: var(--track-h);
  position: relative;
  width: var(--thumb-size);
  height: var(--thumb-size);
  background: var(--color);
  border: solid var(--border-w) var(--color-contrast);
  border-radius: var(--track-border-radius);
  // border-width: 0;
  cursor: pointer;
  transition: border 0.4s;
}

input[type=range] {
  -webkit-appearance: none;
  /* Hides the slider so that custom slider can be made */
  width: 100%;
  /* Specific width is required for Firefox. */
  height: 100%;
  /* Specific width is required for Firefox. */
  background: transparent;
  /* Otherwise white in Chrome */
  min-height: unset;

  &:focus {
    // outline: none; /* Removes the blue border. You should probably do some kind of focus styling for accessibility reasons though. */
    // box-shadow: none;
    // border: none;
  }

  &::-moz-focus-inner {
    outline: none;
    border: none;
  }

  &::-ms-track {
    width: 100%;
    cursor: pointer;
    /* Hides the slider so custom styles can be added */
    background: transparent;
    border-color: transparent;
    color: transparent;
  }

  &::-webkit-slider-runnable-track,
  &::-moz-range-track,
  &::-ms-track {
    @include slider-track;
  }

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    @include slider-thumb;
  }

  &::-moz-range-thumb,
  &::-ms-thumb {
    @include slider-thumb;
  }

  // &::-webkit-slider-thumb:focus,
  // &::-moz-range-thumb:focus {
  //   // box-shadow: 0 0 0 5px red;
  //   // border-width: 5px;
  // }
}
</style>
