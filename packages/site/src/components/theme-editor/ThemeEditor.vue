<script setup lang="ts">
import type { mastodon } from '#shared/types'
import { displayableOklch } from '~/lib/color/culori'

type Theme = mastodon.v1.AccountTheme

const { account } = useAuth()

const {
  themeCssVars,
  setTheme,
  setFont,
  setGridGap,
  setCornerRadius,
  setMainColor,
} = buildTheme2(account.value?.theme)

const theme = computed(() => account.value?.theme ?? defaultThemeConfig)
let originTheme = { ...theme.value }

const workingOKLCH = ref<Theme['colorOklch']>(undefined)
const workingFont = ref<Theme['font']>(undefined)
const workingGridGap = ref<Theme['gridGap']>(undefined)
const workingRadius = ref<Theme['cornerRadius']>(undefined)

watch(() => theme, (theme) => {
  setWorkingValues(theme.value)
}, { immediate: true })

function setWorkingValues(theme: Theme) {
  workingOKLCH.value = theme.colorOklch
  workingFont.value = theme.font
  workingGridGap.value = theme.gridGap
  workingRadius.value = theme.cornerRadius
}

const oklch = computed({
  get: () => {
    if (workingOKLCH.value) {
      return workingOKLCH.value
    }
    return theme.value?.colorOklch
  },
  set: (value) => {
    workingOKLCH.value = value
    if (value) {
      setMainColor(value)
    }
    applyTheme()
  },
})

const origDisplayColor = computed(() => displayableOklch(theme.value?.colorOklch))
const workDisplayColor = computed(() => displayableOklch(workingOKLCH.value))

const cssVars = computed(() => {
  return {
    '--color-original': origDisplayColor.value,
    '--color-working': workDisplayColor.value || origDisplayColor.value,
    '--color-text-working': themeCssVars.value['--color-accent-rgb'],
    '--color-panel-working': themeCssVars.value['--color-panel-rgb'],
  }
})

const fontFaces: Theme['font'][] = ['sans', 'serif', 'mono']

const font = computed({
  get: () => workingFont.value ?? theme.value?.font,
  set: (value) => {
    workingFont.value = value
    if (value) {
      setFont(value)
    }
    applyTheme()
  },
})
const fontThemeClass = computed(() => `theme-font-${font.value}`)

const gridGap = computed({
  get: () => {
    return workingGridGap.value ?? theme.value?.gridGap
  },
  set: (value) => {
    workingGridGap.value = value
    if (value) {
      setGridGap(value)
    }
    applyTheme()
  },
})
const cornerRadius = computed({
  get: () => {
    return workingRadius.value ?? theme.value?.cornerRadius
  },
  set: (value) => {
    workingRadius.value = value
    if (value) {
      setCornerRadius(value)
    }
    applyTheme()
  },
})

const templates: Theme[] = [
  {
    colorOklch: [0.5, 0.0079, 92],
    font: 'mono',
    gridGap: 'small',
    cornerRadius: 'none',
  },
  {
    colorOklch: [0.8, 0.37, 10],
    font: 'sans',
    gridGap: 'normal',
    cornerRadius: 'big',
  },
  {
    colorOklch: [1, 0.3, 92],
    font: 'serif',
    gridGap: 'big',
    cornerRadius: 'small',
  },
]

const templatesPalette = computed<Theme[]>(() => [originTheme, ...templates])

const activeTemplateIndex = computed(() => {
  return templatesPalette.value.findIndex((th) => {
    return th.cornerRadius === workingRadius.value
      && th.font === workingFont.value
      && th.gridGap === workingGridGap.value
      && th.colorOklch?.every((c, i) => c === workingOKLCH.value?.[i])
  })
})

function applyTheme() {
  const newAccount = account.value
  if (newAccount) {
    account.value = {
      ...newAccount,
      theme: {
        colorOklch: workingOKLCH.value ?? theme.value?.colorOklch,
        font: workingFont.value ?? theme.value?.font,
        gridGap: workingGridGap.value ?? theme.value?.gridGap,
        cornerRadius: workingRadius.value ?? theme.value?.cornerRadius,
      },
    }
  }
  // applyThemeToElement(themeElement.value)
  // applyThemeToElement(document?.documentElement)
}

async function saveTheme() {
  const formData = new FormData()
  const colorOklch = workingOKLCH.value ?? theme.value?.colorOklch
  if (colorOklch) {
    formData.append('theme_color_oklch', colorOklch.join(','))
  }
  const font = workingFont.value ?? theme.value?.font
  if (font) {
    formData.append('theme_font', font)
  }
  const gridGap = workingGridGap.value ?? theme.value?.gridGap
  if (gridGap) {
    formData.append('theme_grid_gap', gridGap)
  }
  const cornerRadius = workingRadius.value ?? theme.value?.cornerRadius
  if (cornerRadius) {
    formData.append('theme_corner_radius', cornerRadius)
  }
  await $fetch<mastodon.v1.Account>('/api/accounts/update_credentials', {
    method: 'PATCH',
    body: formData,
  })
  originTheme = { ...theme.value }
  emit('on-save')
}

const emit = defineEmits(['close-request', 'on-save'])

function cancel() {
  setWorkingValues(originTheme)
  applyTheme()
}

function fontButtonFont(fontName: Theme['font']) {
  return fontName === 'mono' ? { fontFamily: 'monospace' } : fontName
}

function applyTemplate(theme: Theme) {
  workingOKLCH.value = theme.colorOklch
  workingFont.value = theme.font
  workingGridGap.value = theme.gridGap
  workingRadius.value = theme.cornerRadius
  setTheme(theme)
  applyTheme()
}

defineExpose({
  saveTheme,
  cancel,
  cacheOriginalTheme: () => {
    originTheme = { ...theme.value }
  },
})
</script>

<template>
  <div class="theme-editor" :class="[fontThemeClass]" :style="cssVars">
    THEME
    <div class="templates-palette">
      <ThemeEditorPresetButton
        v-for="(th, i) in templatesPalette"
        :key="i"
        :theme="th"
        :active="i === activeTemplateIndex"
        @click="applyTemplate(th)"
      />
    </div>
    <ThemeEditorLCHColorPicker v-model:oklch="oklch" />
    <div class="font-panel">
      <div v-for="name in fontFaces" :key="name" class="button-block">
        <button
          :class="{ active: font === name }"
          :style="{ fontFamily: `var(--theme-font-family-${fontButtonFont(name)})` }"
          @click="font = name">
          <div>
            <div>
              Ab
            </div>
            <div class="desc">
              {{ name }}
            </div>
          </div>
        </button>
      </div>
    </div>
    <ThemeEditorThemeGrid v-model:gap="gridGap" />
    <ThemeEditorThemeCornerRadius v-model:radius="cornerRadius" />
    <footer>
      <UiBar padding>
        <button class="cancel" @click="emit('close-request')">
          Cancel
        </button>
        <button class="submit" @click="saveTheme">
          Save
        </button>
      </UiBar>
    </footer>
  </div>
</template>

<style lang='scss'>
.theme-editor {
  --_bg: rgb(236, 236, 236);
  position: sticky;
  width: min(100cqi, 300px);
  background-color: var(--_bg);
  padding: var(--padding-base);
  color: black;
  max-height: var(--dropdown-max-h);
  overflow: auto;
  overscroll-behavior: contain;

  &>* {
    margin-top: var(--padding-base);
  }

  .templates-palette {
    display: flex;
    gap: var(--padding-small);
    justify-content: center;
  }

  .color-picker {
    margin-top: var(--padding-base);
    width: 100%;
  }

  .font-panel {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--padding-base);
    margin-top: var(--padding-base);

    .button-block {
      display: flex;
      flex-flow: column;
      align-items: center;

      button {
        font-size: var(--h3);
      }
    }
  }

  button {
    --button-height: var(--base-size);
    height: auto;
    min-height: var(--base-size);
    min-width: var(--base-size);
    padding-block: var(--padding-small);

    &.active {
      background-color: var(--color-working);
      color: var(--color-text-working);
    }
  }

  footer {
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-block: var(--padding-big);
    position: sticky;
    bottom: 0;

    &::after {
      content: "";
      display: block;
      position: absolute;
      width: 100%;
      height: calc(100% + var(--padding-base));
      bottom: calc(var(--padding-base)* -1);
      background: linear-gradient(0deg, var(--_bg) var(--padding-small), transparent);
      z-index: -1;
    }

    button {
      --button-height: var(--size-three-quarters);
      min-height: unset;
    }

    .cancel {
      background-color: var(--color-original);
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .submit {
      background-color: var(--color-working);
      color: var(--color-text-working);
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}
</style>
