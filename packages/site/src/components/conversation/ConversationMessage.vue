<script setup lang="ts">
import { useFluent } from 'fluent-vue'
import type { ConversationMessage } from '#shared/types'
import type { MenuItem } from '~/types'
import { localizedTime } from '~/composables/dayjs'

const props = defineProps<{
  message: ConversationMessage
  own: boolean
}>()
const emit = defineEmits<{ (evt: 'remove' | 'edit'): void }>()

const disabled = ref(false)

const { $t } = useFluent()
const { $mediaUpload } = useNuxtApp()

const uploading = computed(() => $mediaUpload?.isUploading(props.message.publishId).value)
const uploadProgress = computed<number>(() => (uploading.value && props.message.publishId) ? ($mediaUpload?.progress.get(props.message.publishId)?.percentage ?? 0) : 0)

const toolsMenu = computed<MenuItem[]>(() => [
  {
    label: $t('menu-edit'),
    command: () => emit('edit'),
  },
  {
    label: $t('menu-delete'),
    command: () => emit('remove'),
  },
])
</script>

<template>
  <div class="conversation-message">
    <div class="body" :class="{ disabled }">
      <ContentRich :content="message.content" />
      <div class="tools">
        <UiPopupMenu :items="toolsMenu">
          <template #trigger-title>
            <div v-if="uploading" class="upload-progress">
              <div class="progress-spinner" :style="{ '--progress': uploadProgress + '%' }">
                <div class="progress-circle">
                  <div class="progress-text">{{ Math.round(uploadProgress) }}%</div>
                </div>
              </div>
            </div>
            <span v-else-if="message.state === 'pending'" class="spinner" />
            <span v-else-if="message.state === 'failed'" class="icon-error" />
            <span v-else class="icon-more" />
          </template>
        </UiPopupMenu>
      </div>
    </div>
    <footer>
      <div class="time">
        {{ localizedTime(message.createdAt) }}
      </div>
    </footer>
  </div>
</template>

<style lang="scss">
.conversation-message {
  position: relative;
  background-color: var(--message-bg);
  border-radius: var(--corner-radius) var(--corner-radius) var(--corner-radius) 0;
  width: fit-content;
  overflow: clip;

  &:not(:first-child) {
    border-start-start-radius: 0;
  }

  &:not(:last-child) {
    margin-bottom: 2px;
  }

  .body {
    --corner-radius-link: calc(var(--corner-radius) - var(--padding-small));
    --content-padding: var(--padding-small);
    --content-template-columns: var(--padding-small) 1fr var(--button-height);
    --content-block-margin: var(--content-padding);
    position: relative;
    max-width: 100cqi;

    p {
      max-width: calc(100cqi - var(--content-padding) - var(--button-height));
    }
  }

  // .content {
  //   display: grid;
  //   gap: var(--padding-mini);
  //   grid-template-columns: var(--padding-base) 1fr var(--size-two-third);
  //   // padding: var(--padding-medium) 0 var(--padding-small) var(--padding-base);
  // }

  p {
    margin: 0;
  }

  .tools {
    position: absolute;
    right: 0;
    top: 0;
    display: grid;
    grid-auto-rows: var(--button-height) 1fr;
  }

  footer {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
  }

  .time {
    font-size: var(--font-size-micro);
    text-align: center;
    padding: 0 var(--padding-mini) var(--padding-mini);
    opacity: 0.7;
  }

  .spinner {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    animation: spin 0.8s linear infinite;
  }

  .upload-progress {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-spinner {
    position: relative;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    background: conic-gradient(currentColor var(--progress, 0%), transparent var(--progress, 0%));
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-circle {
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border-radius: 50%;
    background-color: var(--message-bg);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-text {
    font-size: 0.6rem;
    font-weight: 500;
    line-height: 1;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
