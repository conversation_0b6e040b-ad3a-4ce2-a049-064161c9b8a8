<script setup lang="ts">
import { StorageSerializers, useStorage } from '@vueuse/core'
import type { mastodon, ConversationMessage } from '#shared/types'

const popover = ref()

const { $mediaUpload } = useNuxtApp()
const { account } = useAuth()
const { stream } = useStreaming<{ type: string, conversationId: string, status: ConversationMessage }>(computed(() => {
  const accountId = account.value?.id
  return accountId ? `timeline:direct:${accountId}` : undefined
}))

const openConversation = useState<mastodon.v1.Account | null>('open-conversation')
const selectedConversation = useStorage<mastodon.v1.Conversation | undefined>('conversation', null, undefined, { serializer: StorageSerializers.object })

const { data: records, refresh } = await useFetch<mastodon.v1.Conversation[]>('/api/conversations', { default: () => [] })

const conversations = computed<mastodon.v1.Conversation[]>(() => [
  ...records.value,
  ...(selectedConversation.value?.id === '' ? [selectedConversation.value] : []),
])
const unread = computed<boolean>(() => records.value.some(({ unread }) => unread))

watch(records, (items) => {
  if (items.length) {
    if (selectedConversation.value?.id === '') {
      const account = selectedConversation.value.accounts.slice().shift()
      if (account) {
        const conversation = records.value.filter(({ accounts }) => accounts.length === 2).find(({ accounts }) => accounts.find(({ id }) => compareHandle(account.id, id)))
        if (conversation) {
          selectedConversation.value = conversation
        }
      }
    }
    if (!selectedConversation.value) {
      selectedConversation.value = records.value.slice().shift()
    }
  }
}, { immediate: true })

watch(openConversation, (account) => {
  if (account) {
    let conversation = records.value.filter(({ accounts }) => accounts.length === 2).find(({ accounts }) => accounts.find(({ id }) => compareHandle(account.id, id)))
    if (!conversation) {
      conversation = {
        id: '',
        accounts: [account],
        unread: false,
      }
    }
    selectedConversation.value = conversation
    popover.value?.show()
    openConversation.value = null
  }
})

if (import.meta.client) {
  watch(stream, (message) => {
    if (message) {
      const { type, conversationId } = message
      if (type === 'create' && selectedConversation.value?.id !== conversationId) {
        const conversation = records.value.find(({ id }) => id === conversationId)
        if (conversation) {
          conversation.unread = true
        }
        else {
          refresh()
        }
      }
    }
  })

  watch(stream, (message) => {
    if (message) {
      const { type, conversationId, status: record } = message
      const items = useCache<ConversationMessage[]>(computed(() => `conversation-${conversationId}`)).value
      if (items) {
        const index = items.findIndex(({ id, publishId }) => (id && id === record.id) || (publishId && publishId === record.publishId)) ?? -1
        if (index >= 0) {
          $mediaUpload?.clean(items[index]?.publishId)
        }
        if (type === 'create') {
          if (index >= 0) {
            items.splice(index, 1)
          }
          items.unshift(record)
        }
        if (type === 'update') {
          if (index >= 0) {
            items[index] = record
          }
        }
        if (type === 'delete') {
          if (index >= 0) {
            items.splice(index, 1)
          }
        }
      }
    }
  })

  watch($mediaUpload?.error, (value) => {
    if (value) {
      const { conversationId, publishId, error } = value
      console.error(error)
      const items = useCache<ConversationMessage[]>(computed(() => `conversation-${conversationId}`)).value
      if (items) {
        const index = items.findIndex(item => publishId === item.publishId)
        if (index >= 0) {
          items[index] = {
            ...items[index],
            state: 'failed',
          }
        }
      }
    }
  })
}

const shouldShowList = ref(true)
const chatListRef = templateRef('chatListRef')

function selectConversation(conversation: mastodon.v1.Conversation) {
  selectedConversation.value = conversation
}

function handleClickOutsideList(event: Event) {
  const target = event.target as HTMLElement
  const isFocusListButton = target.closest('button.focus-list')
  const isInsideConversationList = chatListRef.value?.$el?.contains(target)

  if (chatListRef.value?.$el && !isInsideConversationList && !isFocusListButton) {
    shouldShowList.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutsideList)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutsideList)
})

async function removeConversation() {
  const conversationId = selectedConversation.value?.id
  if (conversationId) {
    useCache<ConversationMessage[]>(ref(`conversation-${conversationId}`)).value = []
    records.value = records.value.filter(({ id }) => conversationId !== id)
    selectedConversation.value = records.value.slice()?.shift()
    const headers = useRequestHeaders(['cookie'])
    await $fetch(`/api/conversations/${conversationId}`, { method: 'DELETE', headers })
  }
}
</script>

<template>
  <UiPopoverFloat ref="popover" placement="bottom-left" css-class="chat-popover" :opened="refresh">
    <template #trigger-title>
      <span class="icon-chat" />
      <UiUnreadBadge :count="unread" />
    </template>
    <div class="chat-panel" :class="{ 'show-list': shouldShowList }">
      <ConversationList
        ref="chatListRef"
        :conversations="conversations"
        :conversation="selectedConversation"
        @select="selectConversation"
      />
      <ConversationPanel
        v-if="selectedConversation && account"
        :account="account"
        :conversation="selectedConversation"
        @remove="removeConversation"
        @focus-list="shouldShowList = !shouldShowList"
      />
    </div>
  </UiPopoverFloat>
</template>

<style lang='scss'>
.chat-popover {
  --pop-float-bottom: calc(var(--button-height) + var(--padding-big) + var(--padding-base));
  --pop-h: min(var(--base-size) * 15, 100svh - var(--pop-float-bottom) - var(--pop-float-top));
}

.chat-panel {
  container-type: inline-size;
  display: grid;
  grid-template-columns: calc(var(--base-size) + var(--padding-base) * 2) 1fr;
  gap: var(--padding-base);
  background-color: var(--color-panel-minor);
  height: 100%;

  button.focus-list {
    display: none;
  }

  .chat-list {
    --scrollbar-w: 1px;
    width: calc(100% + var(--scrollbar-w));
    padding-inline: var(--padding-base);
    overflow: auto;
    overscroll-behavior: contain;
    // scrollbar-width: thin;
    display: grid;
    grid-auto-rows: max-content;
    gap: var(--padding-medium);
    padding-block: var(--padding-base);
    align-items: center;
    justify-content: center;
    max-height: 100%;
    z-index: 100;
    background-color: var(--color-panel-minor);
    width: fit-content;
    border-radius: var(--border-radius-small);

    &::-webkit-scrollbar {
      width: var(--scrollbar-w);
      height: var(--scrollbar-w);
      background: var(--color-bg);
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-panel);
    }
  }

  .chat-room {
    overflow: auto;
    overscroll-behavior: contain;
    padding: var(--padding-base);
    padding-inline-start: 0;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;

    .chat-list {
      position: absolute;
      top: var(--button-height);
      left: -100%;
      transition: left 0.3s ease-in-out;
      max-height: calc(100% - var(--button-height));
    }

    &.show-list {
      .chat-list {
        left: 0;
      }
    }

    .conversation-header {
      margin-inline-start: var(--padding-small);
      border-radius: calc(var(--corner-radius) - var(--padding-small));
    }

    button.focus-list {
      display: block;
    }

    .conversation-body {
      padding-inline: var(--padding-small);
    }
  }
}
</style>
