<script setup lang="ts">
import { EditorContent } from '@tiptap/vue-3'
import type { mastodon, ConversationMessage } from '#shared/types'
import type { UploadContentMedia } from '~/types'

const props = defineProps<{
  conversation: mastodon.v1.Conversation
  message?: ConversationMessage
}>()
const emit = defineEmits<{
  (evt: 'send', payload: { publishId: string, html: string }): void
  (evt: 'close'): void
}>()

const { $mediaUpload, $audioRecorder } = useNuxtApp()
const { editor, isEmpty, setContent } = useConversationEditor()

const isRecording = computed(() => $audioRecorder?.isRecording.value)
const isEditing = computed(() => Boolean(props.message?.id))

watch(() => props.message, (message) => {
  setContent(message?.content ?? '')
})

function start() {
  editor.value?.setEditable(false)
  editor.value?.chain().focus().startVisualizer().run()
  $audioRecorder?.startRecording()
}

function stop() {
  watchOnce($audioRecorder?.recordingBlob, (blob) => {
    if (blob) {
      const contentMedia = asContentMedia(blob)
      if (contentMedia?.id) {
        const file = new File([blob], contentMedia.id, { lastModified: new Date().getTime(), type: blob.type })
        publish({ files: [{ ...contentMedia, file }] })
      }
    }
  })
  clear()
}

function clear() {
  editor.value?.setEditable(true)
  setContent('')
  $audioRecorder?.stopRecording()
}

function send() {
  const content = editor.value?.getHTML()
  if (content) {
    setContent('')
    publish({ content })
  }
}

async function pickMedia() {
  editor.value?.setEditable(true)
  setContent('')
  const files = await pickFiles()
  publish({ files })
}

function publish({ files, content }: { files?: UploadContentMedia[], content?: string }) {
  const conversationId = props.conversation.id || props.conversation.accounts.slice().shift()?.acct
  if (conversationId) {
    const publishId = props.message?.publishId ?? crypto.randomUUID()
    let html = content ?? ''
    if (files?.length) {
      html = `${createHtml(files, publishId)}${html}`
    }
    $mediaUpload?.send({
      conversationId,
      statusId: props.message?.id,
      publishId,
      uploadMedia: files,
      html,
    })
    emit('send', { publishId, html })
  }
}
</script>

<template>
  <div class="conversation-message-editor" :class="{ editing: isEditing }">
    <EditorContent :editor="editor" class="message-input" />
    <button v-if="!isEmpty" class="compact" @click="send">
      <span :class="isEditing ? 'icon-check' : 'icon-send'" />
    </button>
    <template v-else-if="isRecording">
      <button class="compact" @click="clear">
        <span class="icon-trash" />
      </button>
      <button class="compact" @click="stop">
        <span class="icon-send" />
      </button>
    </template>
    <template v-else-if="!isEditing">
      <button class="compact" @click="start">
        <span class="icon-microphone" />
      </button>
      <button class="compact" @click="pickMedia">
        <span class="icon-media" />
      </button>
    </template>
    <button v-if="isEditing" class="compact" @click="$emit('close')">
      <span class="icon-close" />
    </button>
  </div>
</template>

<style lang='scss'>
.conversation-message-editor {
  display: flex;
  align-items: flex-end;
  padding: var(--padding-base) var(--padding-small);
  width: 100%;
  gap: var(--padding-small);

  &.editing {
    background-color: var(--color-accent-bg);
    border-radius: var(--border-radius-base);
  }

  button {
    flex-shrink: 0;
  }

  .message-input {
    flex-grow: 1;
    max-width: calc(100cqi - var(--button-height) - var(--padding-small) * 2 - var(--padding-base));

    .ProseMirror {
      padding: var(--padding-small) var(--padding-base);
      background: var(--color-panel);
      border-radius: var(--border-radius-base);
      max-height: calc(var(--pop-h) * 0.5);
      overflow: auto;
      overscroll-behavior: contain;

      &.ProseMirror-focused {
        outline: none;
        box-shadow: 0 0 0 3px var(--color-bg);
      }
    }

    .message-rich {
      padding: var(--padding-small);
      background: white;
      border-radius: var(--corner-radius);
    }
  }
}
</style>
