<script setup lang="ts">
import type { mastodon } from '#shared/types'

const { url } = defineProps<{
  url: string
}>()

const isLoading = ref(false)
const isParsed = ref(false)
const card = ref<mastodon.v1.PreviewCard | undefined>(undefined)

onMounted(async () => {
  try {
    isLoading.value = true
    card.value = await $fetch<mastodon.v1.PreviewCard>('/api/microformats', {
      method: 'POST',
      body: { url },
    })
    isParsed.value = !!card.value?.image || !!card.value?.title || !!card.value?.description
  }
  catch (err) {
    console.error(err)
  }
  finally {
    isLoading.value = false
  }
})
</script>

<template>
  <p v-if="isLoading">
    loading...
  </p>
  <a v-if="card" :href="card.url" class="embed-link" target="_blank">
    <img v-if="card.image" :src="card.image">
    <p v-if="!isParsed">{{ card.url }}</p>
    <h3 v-if="card.title">{{ card.title }}</h3>
    <p v-if="card.description">{{ card.description }}</p>
  </a>
</template>

<style scoped>
  .embed-link {
    --bg-color: var(--embed-bg-color, #DFDFDF);
    position: relative;
    display: inline-block;
    font-size: 80%;
    background-color: var(--bg-color);
    color: black;
    border-radius: var(--corner-radius-link, var(--corner-radius-small));
    overflow: clip;
    text-decoration: none;

    & > :not(img, button) {
      padding-inline: var(--padding-small);
      &:last-child {
        padding-bottom: var(--padding-small);
      }
    }

    img {
      width: 100%;
      max-height: 200px;
      object-fit: cover;
    }

    h3, p {
      margin: 0;
    }

    .link {
      word-break: break-all;
    }
  }
</style>
