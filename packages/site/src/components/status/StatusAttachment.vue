<script setup lang="ts">
import { clamp, usePreferredReducedMotion } from '@vueuse/core'
// import type { mastodon } from '#shared/types'
import type { DraftMediaAttachment } from '~/types'

const props = withDefaults(defineProps<{
  attachment: DraftMediaAttachment
  fullSize?: boolean
  cover?: boolean
}>(), {
  fullSize: false,
})

const src = computed(() => props.attachment.previewUrl || props.attachment.url || props.attachment.remoteUrl!)

const rawAspectRatio = computed(() => {
  if (props.attachment.aspect) {
    return props.attachment.aspect
  }
  if (props.attachment.meta?.original?.aspect) {
    return props.attachment.meta.original.aspect
  }
  if (props.attachment.meta?.small?.aspect) {
    return props.attachment.meta.small.aspect
  }
  return undefined
})

const aspectRatio = computed(() => {
  if (props.cover) {
     return undefined }
  if (props.fullSize) {
    return rawAspectRatio.value
  }
  if (rawAspectRatio.value) {
    return clamp(parseFloat(rawAspectRatio.value as string), 0.45, 6)
  }
  return undefined
})

const objectFit = computed(() => {
  return props.cover ? 'cover' : 'contain'
})

const objectPosition = computed(() => {
  if (!props.cover) {
     return undefined }
  const focusX = props.attachment.meta?.focus?.x || 0
  const focusY = props.attachment.meta?.focus?.y || 0
  const x = ((focusX / 2) + 0.5) * 100
  const y = ((focusY / -2) + 0.5) * 100

  return `${x}% ${y}%`
})

const cssClasses = computed(() => {
  return ['media-box', { cover: props.cover }]
})

const typeExtsMap = {
  video: ['mp4', 'webm', 'mov', 'avi', 'mkv', 'flv', 'wmv', 'mpg', 'mpeg'],
  audio: ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma'],
  image: ['jpg', 'jpeg', 'png', 'svg', 'webp', 'bmp'],
  gifv: ['gifv', 'gif'],
}

const type = computed(() => {
  if (props.attachment.type && props.attachment.type !== 'unknown') {
    return props.attachment.type
  }
  // some server returns unknown type, we need to guess it based on file extension
  for (const [type, exts] of Object.entries(typeExtsMap)) {
    if (exts.some(ext => src?.value?.toLowerCase().endsWith(`.${ext}`))) {
      return type
    }
  }
  return 'unknown'
})

const videoUrl = computed(() => {
  if (type.value !== 'video') {
     return '' }
  if (props.attachment.file) {
    return URL.createObjectURL(props.attachment.file)
  }
  return props.attachment.url || props.attachment.previewUrl
})

const video = ref<HTMLVideoElement | undefined>()
const prefersReducedMotion = usePreferredReducedMotion()

useIntersectionObserver(video, (entries) => {
  const ready = video.value?.dataset.ready === 'true'
  if (prefersReducedMotion.value === 'reduce') {
    if (ready && !video.value?.paused) {
      video.value?.pause()
    }
    return
  }

  entries.forEach((entry) => {
    if (entry.intersectionRatio <= 0.75) {
      ready && !video.value?.paused && video.value?.pause()
    }
    else {
      video.value?.play().then(() => {
        video.value!.dataset.ready = 'true'
      }).catch(noop)
    }
  })
}, { threshold: 0.75 })
</script>

<template>
  <!-- <div class="media-box" :style="{ aspectRatio }"> -->
  <template v-if="type === 'video'">
    <video
      ref="video"
      :class="cssClasses"
      preload="none"
      :poster="attachment.previewUrl"
      muted
      loop
      playsinline
      :controls="true"
      :width="attachment.meta?.original?.width"
      :height="attachment.meta?.original?.height"
      :style="{
        aspectRatio,
        objectPosition,
        objectFit,
      }">
      <source :src="videoUrl" type="video">
    </video>
  </template>
  <template v-else-if="type === 'gifv'">
    <video
      ref="video"
      :class="cssClasses"
      preload="none"
      :poster="attachment.previewUrl"
      muted
      loop
      playsinline
      :width="attachment.meta?.original?.width"
      :height="attachment.meta?.original?.height"
      :style="{
        aspectRatio,
        objectPosition,
        objectFit,
      }">
      <source :src="attachment.url || attachment.previewUrl" type="video">
    </video>
  </template>
  <template v-else-if="type === 'audio'">
    <audio controls>
      <source :src="attachment.url || attachment.previewUrl" type="audio">
    </audio>
  </template>
  <template v-else>
    <UiImg
      v-if="src"
      :class="cssClasses"
      :src="src"
      :alt="attachment.description ?? 'Image'"
      :style="{
        aspectRatio,
        objectPosition,
        objectFit,
      }"
    />
  </template>
  <!-- </div> -->
</template>

<style lang="scss">
  :root {
  --size-header-height: var(--base-size, 3rem);
  --size-footer-height: var(--base-size, 3rem);
  --size-max-img-height: calc(100svh - var(--size-header-height) - var(--size-footer-height));
  }
  .media-box {
    position: relative;
    max-width: min(var(--size-max-img-width, 100%), 100%);
    max-height: var(--size-max-img-height);
    aspect-ratio: var(--img-aspect-ratio);
    margin-inline: auto;
    background-color: var(--color-accent-extra-light);
    transition: background-color 0.2s;
    display: block;
    object-fit: contain;

    &.cover {
      width: 100%;
      height: 100%;
    }
  }
</style>
