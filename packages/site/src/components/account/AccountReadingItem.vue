<script setup lang="ts">
import type { RouteLocation } from '#vue-router'

type To = RouteLocation | { name: string, params: Record<string, any> }

defineProps<{
  to: To
  unreadCount?: number
}>()

const emit = defineEmits(['click'])
</script>

<template>
  <li class="reading-list-item">
    <NuxtLink :to="to" class="list-item-link" @click="emit('click')">
      <span class="avatar-wrapper">
        <slot name="avatar" />
        <span v-if="unreadCount && unreadCount > 0" class="unread-badge">{{ unreadCount }}</span>
      </span>
      <div class="list-item-info">
        <h3><slot name="title" /></h3>
        <span class="stats"><slot name="stats" /></span>
      </div>
    </NuxtLink>
  </li>
</template>

<style>
:root {
  --user-color-text: var(--color-text);
  --user-color-bg: var(--color-bg);
}
.reading-list-item {
  --badge-size: var(--base-size);
  --badge-half: calc(var(--badge-size) / 2);
  --c: var(--color-bg);
  --color-accent: var(--user-color-text);

  position: relative;
  display: grid;
  grid-template-columns: var(--badge-size) 1fr var(--button-height);
  gap: 0 var(--padding-small);
  padding: var(--padding-small);
  padding-left: var(--padding-base);
  align-items: center;
  color: var(--user-color-text);
  background-color:  color-mix(in lab, var(--color-bg) 20%, transparent);

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: var(--padding-small);
    background-color: var(--color-bg);
    z-index: -1;
    transition: width 0.2s;
  }

  &:hover {
    --color-accent: var(--color-text);
    background-color:  color-mix(in lab, var(--color-bg) 65%, transparent);
    color: var(--color-text);
  }

  &:has(.router-link-exact-active) {
    --color-accent: var(--color-text);
    &::after {
      width: 100%;
    }
  }

  .list-item-link {
    display: grid;
    grid-column: 1 / 3;
    grid-template-columns: subgrid;
    grid-template-rows: subgrid;
    font-size: var(--font-size-body);
    align-items: center;

    .account-avatar {
      grid-column: 1;
    }

    .list-item-info {
      grid-column: 2;
      .stats {
        font-size: var(--font-size-micro);
      }
    }

    h3 {
      font-size: var(--font-size-body);
      margin: 0;
    }
  }

  a,
  a:hover {
    text-decoration: none;
  }

  .avatar-wrapper {
    position: relative;
    display: inline-block;
  }

  .unread-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    background-color: var(--color-accent);
    color: var(--color-panel);
    border-radius: 9999px;
    padding: 2px 6px;
    font-size: var(--font-size-micro);
    font-weight: bold;
    min-width: 20px;
    text-align: center;
    z-index: 1;
  }
}
</style>
