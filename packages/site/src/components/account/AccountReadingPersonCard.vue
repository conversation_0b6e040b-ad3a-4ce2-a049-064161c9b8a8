<script setup lang="ts">
import type { mastodon } from '#shared/types'

const props = defineProps<{
  account: mastodon.v1.Account
  unreadCount?: number
}>()
const emit = defineEmits(['click'])
const theme = useTheme(computed(() => props.account.theme))
</script>

<template>
  <AccountReadingItem :to="getAccountRoute(account)" class="theme" :style="theme" :unread-count="unreadCount" @click="emit('click')">
    <template #avatar>
      <AccountAvatar :account="account" sizes="96px" />
    </template>
    <template #title>
      {{ getDisplayName(account, { rich: true }) }}
    </template>
    <template #stats>
      <AccountStats :account="account" short />
    </template>
  </AccountReadingItem>
</template>
