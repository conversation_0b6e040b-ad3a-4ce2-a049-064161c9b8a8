<script setup lang="ts">
import type { mastodon } from '#shared/types'

defineProps<{
  account: mastodon.v1.Account
  short?: boolean
  mini?: boolean
}>()
</script>

<template>
  <div class="account-stats theme">
    <span v-if="account.statusesCount" class="n desc">
      {{ $t('account-posts_count', { count: account.statusesCount ?? 0 }) }}
    </span>
    <AccountFollowersFollowingMenu
      v-if="account.followingCount && !short && !mini"
      :account="account"
      role="following"
    />
    <AccountFollowersFollowingMenu
      v-if="account.followersCount && !mini"
      :account="account"
      role="followers"
    />
  </div>
</template>

<style lang="scss">
.account-stats {
  --button-height: var(--size-half);
  --button-padding-inline: var(--padding-small);
  display: flex;
  align-items: center;
  height: var(--button-height);
  margin-inline-start: calc(var(--button-padding-inline) * -1);

  button.compact {
    padding-inline: var(--button-padding-inline);
  }

  &>*:not(:has(button)) {
    padding-inline: var(--button-padding-inline)
  }

  &>*:not(:last-child) {
    border-inline-end: solid 1px color-mix(in lab, var(--color-text) 10%, transparent);
  }
}
// .tippy-box {
//   .account-stats {
//     padding: var(--padding-small) var(--padding-base);
//     height: auto;
//   }
// }
</style>
