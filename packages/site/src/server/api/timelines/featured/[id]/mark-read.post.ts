export default defineEventHandler(async (event) => {
  const person = await requireUserSession(event)
  const featuredId = getRouterParam(event, 'id')

  if (!featuredId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Featured ID is required',
    })
  }

  const db = useEnv().DB

  // Update readDate in properties for actor following
  const actorUpdateSql = `
    UPDATE actor_following_actors
    SET properties = json_set(properties, '$.readDate', datetime('now'))
    WHERE actor_id = ? AND id = ?
  `

  // Update readDate in properties for tag following
  const tagUpdateSql = `
    UPDATE actor_following_tags
    SET properties = json_set(properties, '$.readDate', datetime('now'))
    WHERE actor_id = ? AND id = ?
  `

  // Try updating as actor following first
  const actorResult = await db.prepare(actorUpdateSql).bind(person.id.toString(), featuredId).run()

  if (actorResult.meta.changes === 0) {
    // If no actor following was updated, try updating tag following
    const tagResult = await db.prepare(tagUpdateSql).bind(person.id.toString(), featuredId).run()

    if (tagResult.meta.changes === 0) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Featured item not found',
      })
    }
  }

  return { success: true }
})
