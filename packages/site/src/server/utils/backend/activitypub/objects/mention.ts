import type { <PERSON> } from '~/server/utils/backend/activitypub/objects/link'
import type { Actor } from '~/server/utils/backend/activitypub/actors'
import { urlTo<PERSON>andle } from '~/server/utils/backend/utils/handle'

// https://www.w3.org/TR/activitystreams-vocabulary/#dfn-mention
export type Mention = Link

export function newMention(actor: Actor): Mention {
  return {
    type: 'Mention',
    href: actor.id,
    name: url<PERSON><PERSON><PERSON><PERSON><PERSON>(actor.id),
  }
}
