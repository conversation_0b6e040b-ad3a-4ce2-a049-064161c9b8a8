import { personFromRow } from '../activitypub/actors'
import { NOTE } from '../activitypub/objects/note'
import { loadLocalMastodonAccount } from './account'
import type { TagRow } from './hashtag'
import { toTag } from './hashtag'
import { toMastodonStatusFromRow } from './status'
import type { mastodon } from '#shared/types'
import { PUBLIC_GROUP } from '~/server/utils/backend/activitypub/activities'
import { parseHandle } from '~/server/utils/backend/utils/parse'

export async function search(db: D1Database, domain: string, phrase?: string) {
  let query = phrase?.trim()
  if (query) {
    for (const char of '!@#$%^&*()-_+=~`[]{}|;:\'",.<>\\/?') {
      query = query.replaceAll(char, '')
    }
  }
  console.log('search', phrase, ' > ', query)
  if (query) {
    return {
      statuses: await notes(db, domain, query),
      accounts: await accounts(db, query),
      hashtags: await hashtags(db, query, domain),
    }
  }
  return {
    statuses: [],
    accounts: [],
    hashtags: [],
  }
}

async function notes(db: D1Database, domain: string, query: string, offset: number = 0) {
  const sql = `
    SELECT snippet(objects_search_fts, -1, '<b>', '</b>', '...', 32) AS result,
    objects.*,
    actors.id as actor_id,
    actors.cdate as actor_cdate,
    actors.properties as actor_properties,
    outbox_objects.actor_id as publisher_actor_id,
    (SELECT count(*) FROM actor_favourites WHERE actor_favourites.object_id=objects.id) as favourites_count,
    (SELECT count(*) FROM actor_reblogs WHERE actor_reblogs.object_id=objects.id) as reblogs_count,
    (SELECT count(*) FROM actor_replies WHERE actor_replies.in_reply_to_object_id=objects.id) as replies_count
   FROM objects_search_fts
INNER JOIN objects ON objects_search_fts.rowid = objects.rowid
INNER JOIN outbox_objects ON objects.id = outbox_objects.object_id
INNER JOIN actors ON actors.id=outbox_objects.actor_id
     WHERE objects_search_fts = ?
       AND objects_search_fts.type = '${NOTE}'
       AND json_extract(objects.properties, '$.inReplyTo') IS NULL
       AND outbox_objects.target = '${PUBLIC_GROUP}'
  ORDER BY objects_search_fts.rank
     LIMIT 20
    OFFSET ?
`
  console.debug(sql)
  const stmt = db.prepare(sql).bind(`${query}*`, offset)
  const { success, error, results } = await stmt.all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const statuses: mastodon.v2.StatusSearchResult[] = []
  for (const { result, ...row } of results) {
    const status = await toMastodonStatusFromRow(domain, db, row)
    if (status) {
      statuses.push({
        ...status,
        snippet: result as string,
      })
    }
  }
  return statuses
}

async function accounts(db: D1Database, query: string, offset: number = 0) {
  let phrase = `${query}*`
  if (query.includes('@')) {
    try {
      phrase = parseHandle(query).localPart
    }
    catch {
      // no-op
    }
  }
  const sql = `
            SELECT * FROM actors
            WHERE rowid IN (SELECT rowid FROM actors_search_fts WHERE type='Person' AND actors_search_fts = ? ORDER BY rank LIMIT 20 OFFSET ?)
          `
  console.debug(sql)
  const { results, success, error } = await db.prepare(sql).bind(phrase, offset).all()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  const accounts: mastodon.v1.Account[] = []
  for (let i = 0, len = results.length; i < len; i++) {
    const row: any = results[i]
    const actor = personFromRow(row)
    accounts.push(await loadLocalMastodonAccount(db, actor, true))
  }
  return accounts
}

async function hashtags(db: D1Database, query: string, domain: string, offset: number = 0) {
  const statuses = `
  SELECT count(*)
  FROM outbox_objects
  INNER JOIN objects ON objects.id = outbox_objects.object_id
  INNER JOIN note_tags ON objects.id = note_tags.object_id
  WHERE note_tags.tag_id = tags.id
  AND outbox_objects.target = '${PUBLIC_GROUP}'
  AND objects.type = '${NOTE}'
  AND json_extract(objects.properties, '$.inReplyTo') IS NULL
  `
  const actors = `
  SELECT count(*)
  FROM actor_following_tags
  WHERE actor_following_tags.tag_id = tags.id
  `
  const profiles = `
  SELECT count(*)
  FROM actors
  WHERE EXISTS (SELECT 1 FROM json_each(properties, '$.tags') WHERE value = tags.id)
  `
  const stats = `, (${statuses}) AS statuses_count, (${profiles}) AS profiles_count, (${actors}) AS actors_count`

  const sql = `
            SELECT * ${stats} FROM tags
            WHERE rowid IN (SELECT rowid FROM tags_search_fts WHERE tags_search_fts = ? ORDER BY rank LIMIT 20 OFFSET ?)
          `
  console.debug(sql)
  const { results, success, error } = await db.prepare(sql).bind(`${query}*`, offset).all<TagRow>()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
  if (!results) {
    return []
  }
  return results.map(row => toTag(row, domain))
}
