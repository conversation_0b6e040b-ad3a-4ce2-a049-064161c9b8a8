import type { Actor } from '~/server/utils/backend/activitypub/actors'

export async function insertLike(db: D1Database, actor: Actor, objectId: string) {
  const query = 'INSERT INTO actor_favourites (id, actor_id, object_id) VALUES (?, ?, ?)'
  const { success, error } = await db.prepare(query).bind(crypto.randomUUID(), actor.id.toString(), objectId).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function deleteLike(db: D1Database, actor: Actor, objectId: string) {
  const query = 'DELETE FROM actor_favourites WHERE actor_id = ? AND object_id = ?'
  const { success, error } = await db.prepare(query).bind(actor.id.toString(), objectId).run()
  if (!success) {
    throw new Error('SQL error: ' + error)
  }
}

export async function countLikes(db: D1Database, objectId: string): Promise<number> {
  const query = 'SELECT count(*) as count FROM actor_favourites WHERE object_id=?'
  const out = await db.prepare(query).bind(objectId).first<{ count: number }>()
  return out?.count ?? 0
}
