<template>
  <div class="upload-container">
    <h1>🚀 API Upload Test Client</h1>

    <div
      class="upload-section"
      :class="{ dragover: isDragOver }"
      @dragover.prevent="handleDragOver"
      @dragleave="handleDragLeave"
      @drop.prevent="handleDrop">
      <h3>📁 Select or Drop File</h3>
      <input
        ref="fileInput"
        type="file"
        accept=".pdf,.docx,application/pdf,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        @change="handleFileSelect">
      <p>Or drag and drop a PDF or Word DOCX file here</p>
      <p class="file-info">Supported formats: PDF (.pdf), Word Document (.docx)</p>

      <div class="button-group">
        <button :disabled="!file || isUploading" @click="startUpload">
          {{ isUploading ? 'Uploading...' : 'Upload File' }}
        </button>
        <button :disabled="!isUploading" class="abort-btn" @click="abortUpload">
          Abort Upload
        </button>
      </div>
    </div>

    <div v-if="isUploading" class="progress-container">
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: progress + '%' }" />
      </div>
      <div class="progress-text">{{ progress }}%</div>
    </div>

    <div class="log-container">
      <div
        v-for="(entry, index) in logEntries"
        :key="index"
        :class="['log-entry', entry.type]">
        {{ entry.message }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestUpload',
  data() {
    return {
      file: null,
      uploadId: null,
      currentKey: null,
      parts: [],
      chunkSize: 5 * 1024 * 1024, // 5MB
      isUploading: false,
      aborted: false,
      progress: 0,
      isDragOver: false,
      logEntries: [
        { message: 'Ready to upload files...', type: 'info' },
      ],
    }
  },
  methods: {
    handleFileSelect(event) {
      const file = event.target?.files?.[0] || event
      if (!file) return

      // Validate file type
      const allowedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      ]

      const allowedExtensions = ['.pdf', '.docx']
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))

      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        this.log(`❌ Invalid file type: ${file.name}. Only PDF and DOCX files are allowed.`, 'error')
        this.file = null
        this.$refs.fileInput.value = ''
        return
      }

      this.file = file
      this.log(`✅ Selected file: ${file.name} (${this.formatBytes(file.size)})`, 'info')

      // Log file metadata that will be automatically included
      this.log(`📄 File info - Name: ${file.name}, Type: ${file.type || 'unknown'}, Size: ${this.formatBytes(file.size)}, Last Modified: ${new Date(file.lastModified).toISOString()}`, 'info')
    },

    generateFileMetadata() {
      if (!this.file) {
        return { httpMetadata: {}, customMetadata: {} }
      }

      const httpMetadata = {}
      const customMetadata = {}

      // Set HTTP metadata based on file properties
      if (this.file.type) {
        httpMetadata.contentType = this.file.type
      }
      else {
        // Fallback based on file extension
        const fileExtension = this.file.name.toLowerCase().substring(this.file.name.lastIndexOf('.'))
        if (fileExtension === '.pdf') {
          httpMetadata.contentType = 'application/pdf'
        }
        else if (fileExtension === '.docx') {
          httpMetadata.contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
      }

      // Set custom metadata with file information
      customMetadata['original-filename'] = this.file.name
      customMetadata['file-size'] = this.file.size.toString()
      customMetadata['last-modified'] = new Date(this.file.lastModified).toISOString()
      customMetadata['upload-timestamp'] = new Date().toISOString()

      return { httpMetadata, customMetadata }
    },

    async startUpload() {
      if (!this.file) return

      this.aborted = false
      this.parts = []
      this.isUploading = true
      this.progress = 0

      this.currentKey = this.generateUUID()

      try {
        // Step 1: Create multipart upload
        this.log(`Starting multipart upload for key: ${this.currentKey}`, 'info')
        this.log(`File: ${this.file.name} (${this.formatBytes(this.file.size)})`, 'info')

        // Generate metadata from file properties
        const metadata = this.generateFileMetadata()
        this.log(`Including file metadata: ${JSON.stringify(metadata, null, 2)}`, 'info')

        const createResponse = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(metadata),
        })

        if (!createResponse.ok) {
          throw new Error(`Failed to create upload: ${createResponse.status} ${createResponse.statusText}`)
        }

        const createData = await createResponse.json()
        this.uploadId = createData.uploadId
        this.log(`Created upload with ID: ${this.uploadId}`, 'success')

        // Step 2: Upload parts
        await this.uploadParts()

        // Step 3: Complete upload
        if (!this.aborted) {
          await this.completeUpload()
        }
      }
      catch (error) {
        this.log(`❌ Upload failed: ${error.message}`, 'error')
      }
      finally {
        this.isUploading = false
      }
    },

    async uploadParts() {
      const totalChunks = Math.ceil(this.file.size / this.chunkSize)
      this.log(`Uploading ${totalChunks} parts...`, 'info')

      for (let i = 0; i < totalChunks; i++) {
        if (this.aborted) {
          throw new Error('Upload aborted')
        }

        const start = i * this.chunkSize
        const end = Math.min(start + this.chunkSize, this.file.size)
        const chunk = this.file.slice(start, end)
        const partNumber = i + 1

        this.log(`Uploading part ${partNumber}/${totalChunks} (${this.formatBytes(chunk.size)})`, 'info')

        const response = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}/${this.uploadId}/${partNumber}`, {
          method: 'PUT',
          body: chunk,
        })

        if (!response.ok) {
          throw new Error(`Failed to upload part ${partNumber}: ${response.status} ${response.statusText}`)
        }

        const part = await response.json()
        this.parts.push(part)

        this.progress = Math.round((partNumber / totalChunks) * 100)
        this.log(`Part ${partNumber} uploaded successfully (ETag: ${part.etag})`, 'success')
      }
    },

    async completeUpload() {
      this.log('Completing multipart upload...', 'info')

      const response = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}/${this.uploadId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parts: this.parts,
        }),
      })

      if (!response.ok) {
        throw new Error(`Failed to complete upload: ${response.status} ${response.statusText}`)
      }

      this.log(`Upload completed successfully!`, 'success')
      this.progress = 100
    },

    async abortUpload() {
      if (!this.uploadId) return

      this.aborted = true
      this.log('Aborting upload...', 'info')

      try {
        const response = await fetch(`/api/upload/${encodeURIComponent(this.currentKey)}/${this.uploadId}`, {
          method: 'DELETE',
        })

        if (response.ok) {
          this.log('Upload aborted successfully', 'info')
        }
        else {
          this.log('Failed to abort upload', 'error')
        }
      }
      catch (error) {
        this.log(`Error aborting upload: ${error.message}`, 'error')
      }
      finally {
        this.isUploading = false
        this.progress = 0
      }
    },

    handleDragOver() {
      this.isDragOver = true
    },

    handleDragLeave() {
      this.isDragOver = false
    },

    handleDrop(event) {
      this.isDragOver = false
      const files = event.dataTransfer.files
      if (files.length > 0) {
        this.handleFileSelect(files[0])
      }
    },

    generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0
        const v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    },

    formatBytes(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    log(message, type = 'info') {
      this.logEntries.push({
        message: `${new Date().toLocaleTimeString()} - ${message}`,
        type,
      })

      // Keep only last 50 entries
      if (this.logEntries.length > 50) {
        this.logEntries = this.logEntries.slice(-50)
      }
    },
  },
}
</script>

<style scoped>
.upload-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
}

.upload-container > * {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

h1 {
  color: #333;
  margin-bottom: 30px;
  text-align: center;
}

.upload-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
}

.upload-section.dragover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.file-info {
  font-size: 0.9em;
  color: #888;
  margin-top: 5px;
}

input[type="file"] {
  margin: 10px 0;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.button-group {
  margin-top: 20px;
}

button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
  font-size: 14px;
  transition: background-color 0.2s;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.abort-btn {
  background: #dc3545;
}

.abort-btn:hover:not(:disabled) {
  background: #c82333;
}

.progress-container {
  margin: 20px 0;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
  border-radius: 10px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  font-weight: bold;
  color: #007bff;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
}

.log-entry {
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.info {
  color: #17a2b8;
}

.log-entry.success {
  color: #28a745;
}

.log-entry.error {
  color: #dc3545;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 600px) {
  .upload-container {
    padding: 10px;
  }

  .upload-container > * {
    padding: 20px;
  }

  button {
    display: block;
    width: 100%;
    margin: 10px 0;
  }
}
</style>
