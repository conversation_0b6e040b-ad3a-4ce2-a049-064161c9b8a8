# Claude Code Memory File for individuallist.xyz

This file contains repository-specific instructions and guidelines for Claude Code when working on this codebase.

## Code Style Guidelines

### Comments
- **DO NOT ADD COMMENTS** to code unless explicitly requested by the user
- Keep code clean and self-documenting through clear naming and structure
- Only add comments when the user specifically asks for them

## Repository Context
This is the individuallist.xyz project repository.

## Development Guidelines
- Follow existing code patterns and conventions in the codebase
- Maintain consistency with the existing code style
- Test changes when possible before committing

## Additional Notes
- When making changes, focus on clean, readable code without unnecessary comments
- Prefer descriptive variable and function names over inline comments